"""
可视化工具模块

提供模型预测结果的可视化功能
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
from typing import Dict, List, Optional, Tuple
import logging


def setup_plot_style():
    """设置绘图样式"""
    plt.style.use("default")
    sns.set_palette("husl")
    plt.rcParams["font.sans-serif"] = ["SimHei", "DejaVu Sans"]
    plt.rcParams["axes.unicode_minus"] = False
    plt.rcParams["figure.dpi"] = 100


def categorize_columns(target_columns: List[str]) -> Dict[str, List[str]]:
    """
    将目标列按类型分类

    Args:
        target_columns: 目标列名列表

    Returns:
        分类后的列名字典
    """
    categories = {
        "furnace_temp": [],  # 炉温数据
        "strip_temp": [],  # 板温数据
        "other": [],  # 其他数据
    }

    for col in target_columns:
        if col.startswith("STRIP_TEMP_"):
            categories["strip_temp"].append(col)
        elif col.startswith("TEMP_"):
            categories["furnace_temp"].append(col)
        else:
            categories["other"].append(col)

    return categories


def plot_prediction_comparison(
    actual: np.ndarray,
    predictions: np.ndarray,
    target_columns: List[str],
    file_index: int = 0,
    save_path: Optional[str] = None,
    show_confidence: bool = True,
    max_samples: int = 1000,
) -> None:
    """
    绘制预测结果与实际结果的对比图表

    Args:
        actual: 实际值数组，形状为 (n_samples, n_targets)
        predictions: 预测值数组，形状为 (n_samples, n_targets)
        target_columns: 目标列名列表
        file_index: 文件索引，用于标题显示
        save_path: 保存路径，如果为None则不保存
        show_confidence: 是否显示置信区间
        max_samples: 最大显示样本数，用于控制图表清晰度
    """
    logger = logging.getLogger(__name__)

    try:
        setup_plot_style()

        # 限制样本数量以提高可视化效果
        n_samples = min(len(actual), max_samples)
        if n_samples < len(actual):
            indices = np.linspace(0, len(actual) - 1, n_samples, dtype=int)
            actual = actual[indices]
            predictions = predictions[indices]
            logger.info(
                f"为提高可视化效果，从 {len(actual)} 个样本中采样 {n_samples} 个"
            )

        # 分类列
        categories = categorize_columns(target_columns)

        # 计算子图布局
        total_plots = sum(1 for cat_cols in categories.values() if cat_cols)
        if total_plots == 0:
            logger.warning("没有可绘制的数据列")
            return

        fig_height = max(6, total_plots * 4)
        fig, axes = plt.subplots(total_plots, 1, figsize=(15, fig_height))

        if total_plots == 1:
            axes = [axes]

        plot_idx = 0
        x_axis = np.arange(len(actual))

        # 绘制炉温数据
        if categories["furnace_temp"]:
            ax = axes[plot_idx]
            _plot_category_data(
                ax,
                actual,
                predictions,
                categories["furnace_temp"],
                target_columns,
                x_axis,
                "炉温数据",
                confidence_interval=10 if show_confidence else None,
            )
            plot_idx += 1

        # 绘制板温数据
        if categories["strip_temp"]:
            ax = axes[plot_idx]
            _plot_category_data(
                ax,
                actual,
                predictions,
                categories["strip_temp"],
                target_columns,
                x_axis,
                "板温数据",
                confidence_interval=5 if show_confidence else None,
            )
            plot_idx += 1

        # 绘制其他数据
        if categories["other"]:
            ax = axes[plot_idx]
            _plot_category_data(
                ax,
                actual,
                predictions,
                categories["other"],
                target_columns,
                x_axis,
                "其他数据",
                confidence_interval=None,
            )
            plot_idx += 1

        # 设置总标题
        fig.suptitle(
            f"模型预测结果对比 - 文件 {file_index + 1}", fontsize=16, fontweight="bold"
        )

        # 调整布局
        plt.tight_layout()
        plt.subplots_adjust(top=0.95)

        # 保存图表
        if save_path:
            save_file = (
                Path(save_path) / f"prediction_comparison_file_{file_index + 1}.png"
            )
            save_file.parent.mkdir(parents=True, exist_ok=True)
            plt.savefig(save_file, dpi=300, bbox_inches="tight")
            logger.info(f"图表已保存到: {save_file}")

        plt.show()

    except Exception as e:
        logger.error(f"绘制预测对比图失败: {e}")
        raise


def _plot_category_data(
    ax,
    actual: np.ndarray,
    predictions: np.ndarray,
    category_columns: List[str],
    all_columns: List[str],
    x_axis: np.ndarray,
    category_name: str,
    confidence_interval: Optional[float] = None,
) -> None:
    """
    绘制特定类别的数据

    Args:
        ax: matplotlib轴对象
        actual: 实际值数组
        predictions: 预测值数组
        category_columns: 当前类别的列名
        all_columns: 所有列名
        x_axis: x轴数据
        category_name: 类别名称
        confidence_interval: 置信区间范围（±度数）
    """
    colors = sns.color_palette("husl", len(category_columns))

    for i, col in enumerate(category_columns):
        col_idx = all_columns.index(col)
        color = colors[i]

        # 绘制实际值
        ax.plot(
            x_axis,
            actual[:, col_idx],
            label=f"{col} (实际)",
            color=color,
            linewidth=1.5,
            alpha=0.8,
        )

        # 绘制预测值
        ax.plot(
            x_axis,
            predictions[:, col_idx],
            label=f"{col} (预测)",
            color=color,
            linewidth=1.5,
            linestyle="--",
            alpha=0.8,
        )

        # 绘制置信区间
        if confidence_interval is not None:
            actual_col = actual[:, col_idx]
            upper_bound = actual_col + confidence_interval
            lower_bound = actual_col - confidence_interval

            ax.fill_between(
                x_axis,
                lower_bound,
                upper_bound,
                color=color,
                alpha=0.2,
                label=f"{col} (±{confidence_interval}°C)",
            )

    ax.set_title(f"{category_name} - 预测 vs 实际", fontsize=14, fontweight="bold")
    ax.set_xlabel("样本序号", fontsize=12)
    ax.set_ylabel("温度 (°C)", fontsize=12)
    ax.legend(bbox_to_anchor=(1.05, 1), loc="upper left")
    ax.grid(True, alpha=0.3)


def plot_rolling_prediction_comparison(
    actual: np.ndarray,
    predictions: np.ndarray,
    target_columns: List[str],
    rolling_steps: int,
    key_steps: List[int],
    file_index: int = 0,
    save_path: Optional[str] = None,
    max_sequences: int = 100,
) -> None:
    """
    绘制滚动预测结果对比图表

    Args:
        actual: 实际值数组，形状为 (n_sequences, rolling_steps, n_targets)
        predictions: 预测值数组，形状为 (n_sequences, rolling_steps, n_targets)
        target_columns: 目标列名列表
        rolling_steps: 滚动预测步数
        key_steps: 关键步数列表
        file_index: 文件索引
        save_path: 保存路径
        max_sequences: 最大显示序列数
    """
    logger = logging.getLogger(__name__)

    try:
        setup_plot_style()

        # 限制序列数量
        n_sequences = min(len(actual), max_sequences)
        if n_sequences < len(actual):
            indices = np.linspace(0, len(actual) - 1, n_sequences, dtype=int)
            actual = actual[indices]
            predictions = predictions[indices]

        # 分类列
        categories = categorize_columns(target_columns)

        # 为每个关键步数创建图表
        for step in key_steps:
            if step > rolling_steps:
                continue

            step_idx = step - 1  # 转换为0索引

            # 提取当前步数的数据
            step_actual = actual[:, step_idx, :]
            step_predictions = predictions[:, step_idx, :]

            # 绘制对比图
            step_save_path = None
            if save_path:
                if save_path.endswith(".png"):
                    step_save_path = save_path.replace(".png", f"_step_{step}.png")
                else:
                    step_save_path = f"{save_path}/step_{step}"

            plot_prediction_comparison(
                step_actual,
                step_predictions,
                target_columns,
                file_index=file_index,
                save_path=step_save_path,
                show_confidence=True,
                max_samples=n_sequences,
            )

            logger.info(f"已绘制第 {step} 步滚动预测对比图")

    except Exception as e:
        logger.error(f"绘制滚动预测对比图失败: {e}")
        raise


def calculate_and_plot_metrics_summary(
    results: List[Dict], target_columns: List[str], save_path: Optional[str] = None
) -> None:
    """
    计算并绘制多文件预测指标汇总

    Args:
        results: 预测结果列表
        target_columns: 目标列名列表
        save_path: 保存路径
    """
    logger = logging.getLogger(__name__)

    try:
        setup_plot_style()

        if not results:
            logger.warning("没有预测结果可供汇总")
            return

        # 提取指标数据
        metrics_data = []
        for i, result in enumerate(results):
            if "metrics" in result and "per_column" in result["metrics"]:
                for col, metrics in result["metrics"]["per_column"].items():
                    metrics_data.append(
                        {
                            "file_index": i + 1,
                            "column": col,
                            "RMSE": metrics["RMSE"],
                            "MAE": metrics["MAE"],
                            "R2": metrics["R2"],
                        }
                    )

        if not metrics_data:
            logger.warning("没有有效的指标数据")
            return

        df_metrics = pd.DataFrame(metrics_data)

        # 创建指标汇总图
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))

        # RMSE 箱线图
        sns.boxplot(data=df_metrics, x="column", y="RMSE", ax=axes[0, 0])
        axes[0, 0].set_title("RMSE 分布", fontsize=14, fontweight="bold")
        axes[0, 0].tick_params(axis="x", rotation=45)

        # MAE 箱线图
        sns.boxplot(data=df_metrics, x="column", y="MAE", ax=axes[0, 1])
        axes[0, 1].set_title("MAE 分布", fontsize=14, fontweight="bold")
        axes[0, 1].tick_params(axis="x", rotation=45)

        # R2 箱线图
        sns.boxplot(data=df_metrics, x="column", y="R2", ax=axes[1, 0])
        axes[1, 0].set_title("R² 分布", fontsize=14, fontweight="bold")
        axes[1, 0].tick_params(axis="x", rotation=45)

        # 各文件整体指标对比
        overall_metrics = []
        for i, result in enumerate(results):
            if "metrics" in result and "overall" in result["metrics"]:
                overall_metrics.append(
                    {
                        "file_index": i + 1,
                        "RMSE": result["metrics"]["overall"]["RMSE"],
                        "MAE": result["metrics"]["overall"]["MAE"],
                        "R2": result["metrics"]["overall"]["R2"],
                    }
                )

        if overall_metrics:
            df_overall = pd.DataFrame(overall_metrics)
            x_pos = np.arange(len(df_overall))

            axes[1, 1].bar(
                x_pos - 0.2, df_overall["RMSE"], 0.4, label="RMSE", alpha=0.8
            )
            axes[1, 1].bar(x_pos + 0.2, df_overall["MAE"], 0.4, label="MAE", alpha=0.8)

            axes[1, 1].set_title("各文件整体指标对比", fontsize=14, fontweight="bold")
            axes[1, 1].set_xlabel("文件序号")
            axes[1, 1].set_ylabel("误差值")
            axes[1, 1].set_xticks(x_pos)
            axes[1, 1].set_xticklabels([f"文件{i}" for i in df_overall["file_index"]])
            axes[1, 1].legend()

        plt.suptitle("预测指标汇总分析", fontsize=16, fontweight="bold")
        plt.tight_layout()
        plt.subplots_adjust(top=0.93)

        # 保存图表
        if save_path:
            save_file = Path(save_path) / "metrics_summary.png"
            save_file.parent.mkdir(parents=True, exist_ok=True)
            plt.savefig(save_file, dpi=300, bbox_inches="tight")
            logger.info(f"指标汇总图已保存到: {save_file}")

        plt.show()

    except Exception as e:
        logger.error(f"绘制指标汇总图失败: {e}")
        raise
